<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Portfolio Metrics</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
  />
  <style>
    @import url("https://fonts.googleapis.com/css2?family=Inter&display=swap");
    body {
      font-family: "Inter", sans-serif;
    }
  </style>
</head>
<body class="bg-[#f0ebe8] text-[#3a3a3a]">
  <div class="max-w-[1280px] mx-auto px-6 py-10">
    <div class="border-t border-[#6b6b6b]"></div>
    <div
      class="mt-10 grid grid-cols-1 sm:grid-cols-3 gap-x-12 gap-y-12 text-[15px] leading-[1.4] font-normal"
    >
      <!-- Column 1 -->
      <div class="flex flex-col">
        <div class="flex justify-between items-start mb-3">
          <p class="max-w-[140px] leading-[1.4] font-normal text-[#3a3a3a]">
            Managed portfolio carbon footprint
          </p>
          <p class="text-[#3a3a3a] text-[15px] leading-[1.4] font-normal">tCO₂e</p>
        </div>
        <div class="flex items-center mb-6">
          <p class="text-[40px] leading-[1.1] font-light tracking-tight text-[#3a3a3a]">
            45,048
          </p>
          <div class="ml-6 text-[15px] leading-[1.4] font-normal text-[#a87f7a] flex flex-col items-center">
            <span class="text-[11px] leading-[1.4] font-normal mb-1">from 2019</span>
            <span class="flex items-center gap-1 font-light">
              <i class="fas fa-arrow-up text-[#a87f7a] text-[14px]"></i>16%
            </span>
          </div>
        </div>
        <div class="space-y-4 mb-8">
          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1">
            <span>2022</span>
            <span>45,048</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#6b3f3f]"
              style="width: 60%"
              aria-label="Progress bar for 2022 carbon footprint"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2021</span>
            <span>14,111</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 20%"
              aria-label="Progress bar for 2021 carbon footprint"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2020</span>
            <span>32,813</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 45%"
              aria-label="Progress bar for 2020 carbon footprint"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2019</span>
            <span>38,673</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 55%"
              aria-label="Progress bar for 2019 carbon footprint"
            ></div>
          </div>
        </div>
        <button
          class="flex items-center gap-3 text-[13px] font-normal text-[#3a3a3a] hover:underline w-max"
          aria-label="See full breakdown of carbon footprint"
        >
          <span>See full breakdown of carbon footprint</span>
          <span
            class="w-10 h-10 rounded-full border border-[#3a3a3a] flex justify-center items-center text-[18px]"
            aria-hidden="true"
            ><i class="fas fa-arrow-right"></i
          ></span>
        </button>
      </div>

      <!-- Column 2 -->
      <div class="flex flex-col">
        <div class="flex justify-between items-start mb-3">
          <p class="max-w-[140px] leading-[1.4] font-normal text-[#3a3a3a]">
            Managed portfolio energy intensity
          </p>
          <p class="text-[#3a3a3a] text-[15px] leading-[1.4] font-normal">kWh/m²</p>
        </div>
        <div class="flex items-center mb-6">
          <p class="text-[40px] leading-[1.1] font-light tracking-tight text-[#3a3a3a]">
            123
          </p>
          <div class="ml-6 text-[15px] leading-[1.4] font-normal text-[#a87f7a] flex flex-col items-center">
            <span class="text-[11px] leading-[1.4] font-normal mb-1">from 2019</span>
            <span class="flex items-center gap-1 font-light">
              <i class="fas fa-arrow-down text-[#a87f7a] text-[14px]"></i>22%
            </span>
          </div>
        </div>
        <div class="space-y-4 mb-8">
          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1">
            <span>2022</span>
            <span>123</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#6b3f3f]"
              style="width: 40%"
              aria-label="Progress bar for 2022 energy intensity"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2021</span>
            <span>128</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 45%"
              aria-label="Progress bar for 2021 energy intensity"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2020</span>
            <span>135</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 50%"
              aria-label="Progress bar for 2020 energy intensity"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2019</span>
            <span>157</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 60%"
              aria-label="Progress bar for 2019 energy intensity"
            ></div>
          </div>
        </div>
        <button
          class="flex items-center gap-3 text-[13px] font-normal text-[#3a3a3a] hover:underline w-max"
          aria-label="Download the data for energy intensity"
        >
          <span>Download the data</span>
          <span
            class="w-10 h-10 rounded-full border border-[#3a3a3a] flex justify-center items-center text-[18px]"
            aria-hidden="true"
            ><i class="fas fa-arrow-down"></i
          ></span>
        </button>
      </div>

      <!-- Column 3 -->
      <div class="flex flex-col">
        <div class="flex justify-between items-start mb-3">
          <p class="max-w-[140px] leading-[1.4] font-normal text-[#3a3a3a]">
            Managed portfolio energy consumption
          </p>
          <p class="text-[#3a3a3a] text-[15px] leading-[1.4] font-normal">kWh</p>
        </div>
        <div class="flex items-center mb-6">
          <p class="text-[40px] leading-[1.1] font-light tracking-tight text-[#3a3a3a]">
            47,790,662
          </p>
          <div class="ml-6 text-[15px] leading-[1.4] font-normal text-[#a87f7a] flex flex-col items-center">
            <span class="text-[11px] leading-[1.4] font-normal mb-1">from 2019</span>
            <span class="flex items-center gap-1 font-light">
              <i class="fas fa-arrow-down text-[#a87f7a] text-[14px]"></i>27%
            </span>
          </div>
        </div>
        <div class="space-y-4 mb-8">
          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1">
            <span>2022</span>
            <span>47,790,662</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#6b3f3f]"
              style="width: 40%"
              aria-label="Progress bar for 2022 energy consumption"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2021</span>
            <span>49,324,077</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 45%"
              aria-label="Progress bar for 2021 energy consumption"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2020</span>
            <span>48,784,205</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 45%"
              aria-label="Progress bar for 2020 energy consumption"
            ></div>
          </div>

          <div class="flex justify-between text-[13px] font-normal text-[#3a3a3a] mb-1 mt-4">
            <span>2019</span>
            <span>65,198,706</span>
          </div>
          <div class="h-4 rounded-full bg-[#d9d4d2] relative overflow-hidden">
            <div
              class="h-4 rounded-full bg-[#a87f7a]"
              style="width: 60%"
              aria-label="Progress bar for 2019 energy consumption"
            ></div>
          </div>
        </div>
        <button
          class="flex items-center gap-3 text-[13px] font-normal text-[#3a3a3a] hover:underline w-max"
          aria-label="Download the data for energy consumption"
        >
          <span>Download the data</span>
          <span
            class="w-10 h-10 rounded-full border border-[#3a3a3a] flex justify-center items-center text-[18px]"
            aria-hidden="true"
            ><i class="fas fa-arrow-down"></i
          ></span>
        </button>
      </div>
    </div>
  </div>
</body>
</html>